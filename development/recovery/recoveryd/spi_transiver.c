/*
spi transiver file
*/

// 添加必要的头文件
#include <pthread.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <linux/types.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>
#include <sys/reboot.h>
#include <ctype.h>  // 用于 tolower 函数
#include <sys/stat.h>
#include "cJSON.h"

#include "spi_message.h"
#include "spi_transiver.h"
#include "bootloader_message/bootloader_message.h"

// 线程控制结构体
typedef struct {
    pthread_t thread_id;
    int running;
    pthread_mutex_t mutex;
    pthread_cond_t cond;
} spi_thread_control_t;

// 全局线程控制变量
static spi_thread_control_t g_spi_thread = {
    .running = 0,
    .mutex = PTHREAD_MUTEX_INITIALIZER,
    .cond = PTHREAD_COND_INITIALIZER
};

#define MD5_SIZE 32  // MD5 值长度为 32 字节（十六进制字符串形式）
#define SPI_MSG_DEFAULTE_FRAME_SIZE                     (256 + 10)
// 假设最大消息长度不会超过 1K
#define SPI_MSG_MAX_SIZE                                (2048 + 10)
#define SPI_FILE_TRANSFER_FRAME_SIZE                    SPI_MSG_MAX_SIZE

// 全局变量
int g_spi_ota_phase = SPI_OTA_PHASE_CMD_IDLE;

static const char* gDataBlock = "/dev/mtdblock0";
static char g_expected_md5[MD5_SIZE + 1];  // 存储期望的 MD5 值
static int g_file_tol_len = 0;
static int g_recv_process = 0;
static const char* spi_ota_file = "/rom/mnt/ota/updater.tar.gz";
static uint16_t g_spi_recv_len = SPI_MSG_DEFAULTE_FRAME_SIZE;

// SPI 相关定义
#define SPI_DEVICE          "/dev/spidev0.0"         //0.0: 1.8V; 1.0: 3.3V
#define SPI_SPEED           7000000  // 1MHz
#define SPI_BITS_PER_WORD   8
#define SPI_MODE            0
#define SPI_DELAY           100     //usec

// 添加调试宏
#define DEBUG_SPI_MSG 0

#if DEBUG_SPI_MSG
#define SPI_MSG_DEBUG(fmt, ...) printf("SPI_MSG: " fmt "\n", ##__VA_ARGS__)
#else
#define SPI_MSG_DEBUG(fmt, ...)
#endif

// 函数声明
static int init_spi(void);
static int calculate_file_md5(const char* file_path, char* md5_str);
static int compare_md5(const char* md51, const char* md52);
static void print_md5(const char* prefix, const char* md5);
static int handle_spi_message(int spi_fd, const spi_msg_header_t* header, const char* data, size_t data_len);
static void* spi_receive_thread(void* arg);

static uint8_t TxBuffer[SPI_MSG_DEFAULTE_FRAME_SIZE];

// SPI 初始化函数
static int init_spi(void) {
    int fd = open(SPI_DEVICE, O_RDWR);
    if (fd < 0) {
        printf("Failed to open SPI device: %s\n", strerror(errno));
        return -1;
    }

    // 设置 SPI 模式
    uint32_t mode = SPI_MODE;
    if (ioctl(fd, SPI_IOC_WR_MODE32, &mode) < 0) {
        printf("Failed to set wr SPI mode: %s\n", strerror(errno));
        close(fd);
        return -1;
    }
    if (ioctl(fd, SPI_IOC_RD_MODE32, &mode) < 0) {
        printf("Failed to set rd SPI mode: %s\n", strerror(errno));
        close(fd);
        return -1;
    }

    // 设置位宽
    uint8_t bits = SPI_BITS_PER_WORD;
    if (ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits) < 0) {
        printf("Failed to set wr bits per word: %s\n", strerror(errno));
        close(fd);
        return -1;
    }
    if (ioctl(fd, SPI_IOC_RD_BITS_PER_WORD, &bits) < 0) {
        printf("Failed to set rd bits per word: %s\n", strerror(errno));
        close(fd);
        return -1;
    }

    // 设置速度
    uint32_t speed = SPI_SPEED;
    if (ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed) < 0) {
        printf("Failed to set wr SPI speed: %s\n", strerror(errno));
        close(fd);
        return -1;
    }
    if (ioctl(fd, SPI_IOC_RD_MAX_SPEED_HZ, &speed) < 0) {
        printf("Failed to set rd SPI speed: %s\n", strerror(errno));
        close(fd);
        return -1;
    }

    return fd;
}

static ssize_t spi_read(int fd, uint8_t * rx, size_t len)
{
    int ret;
    struct spi_ioc_transfer tr = {
        .tx_buf = 0,
        .rx_buf = (unsigned long)rx,
        .len = len,
        .delay_usecs = SPI_DELAY,
        .speed_hz = SPI_SPEED,
        .bits_per_word = SPI_BITS_PER_WORD
    };

    ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
    if (ret < 1) {
        printf("can't read spi message, ret:%d\n", ret);
    }

    return ret;
}

static ssize_t spi_write(int fd, const uint8_t * tx, size_t len)
{
    int ret;
    struct spi_ioc_transfer tr = {
        .tx_buf = (unsigned long)tx,
        .rx_buf = 0,
        .len = len,
        .delay_usecs = SPI_DELAY,
        .speed_hz = SPI_SPEED,
        .bits_per_word = SPI_BITS_PER_WORD
    };
    ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
    if (ret < 1) {
        printf("can't write spi message, ret:%d\n", ret);
    }

    return ret;
}

// 计算CRC16
static uint16_t calculate_crc16(const uint8_t *data, size_t length) {
    uint16_t crc = 0xFFFF;
    
    for (size_t i = 0; i < length; i++) {
        crc ^= (uint16_t)data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    
    return crc;
}

static int send_response(int spi_fd, uint16_t cmd_id, uint8_t type, uint8_t status, uint8_t *data, uint16_t data_length)
{
    spi_msg_header_t *header = (spi_msg_header_t *)TxBuffer;
    
    memset(TxBuffer, 0, sizeof(TxBuffer));

    // 设置前导码为0xAAAA
    memset(header->preamble, 0xAA, MSG_PREAMBLE_SIZE);
    
    // 设置命令类型和ID
    header->cmd_type = type; // 使用传入的类型参数
    header->cmd_id = cmd_id;
    
    // 设置状态码
    header->status = status;
    
    // 设置数据长度
    header->data_length = data_length;
    
    printf("header len: 0x%02x\n", header->data_length);

    // 复制数据
    if (data_length > 0 && data != NULL) {
        memcpy(TxBuffer + sizeof(spi_msg_header_t), data, data_length);
    }
    
    // 计算CRC
    header->crc = calculate_crc16(TxBuffer + sizeof(spi_msg_header_t), data_length);

#if DEBUG_SPI_MSG
    for(int i = 0; i < (sizeof(spi_msg_header_t) + data_length); i++) {
        printf("%02X ", TxBuffer[i]);
    }
    printf("\n");
#endif

    // 发送确认消息, ATTENTION: the spi_write will be pend here until the ack is read from host !!!
    ssize_t sent = spi_write(spi_fd, (uint8_t *)TxBuffer, (sizeof(spi_msg_header_t) + data_length));
    if (sent != (sizeof(spi_msg_header_t) + data_length)) {
        printf("Failed to send ACK message: %s\n", strerror(errno));
        return -1;
    }

    printf("send response id: 0x%02x OK\n", cmd_id);

    return 0;
}

static int send_ack_message(int spi_fd, uint16_t cmd_id, uint8_t status) {
    spi_msg_header_t ack_header = {
        .preamble = {0xAA, 0xAA},  // 前导码
        .cmd_type = CMD_TYPE_GET_RESP,
        .cmd_id = cmd_id,
        .status = status,
        .crc = 0,
        .data_length = 0
    };

    // 计算CRC
    if(ack_header.data_length) {
        ack_header.crc = calculate_crc16((uint8_t*)&ack_header + MSG_PREAMBLE_SIZE, 
                                    ack_header.data_length);
    }

#if DEBUG_SPI_MSG
    for(int i = 0; i < (sizeof(spi_msg_header_t)); i++) {
        printf("%02X ", TxBuffer[i]);
    }
    printf("\n");
#endif

    // 发送确认消息, ATTENTION: the spi_write will be pend here until the ack is read from host !!!
    ssize_t sent = spi_write(spi_fd, (uint8_t *)&ack_header, (sizeof(spi_msg_header_t) + ack_header.data_length));
    if (sent != (sizeof(spi_msg_header_t) + ack_header.data_length)) {
        printf("Failed to send ACK message: %s\n", strerror(errno));
        return -1;
    }

    printf("send_ack_message ID: %02X OK\n", cmd_id);

    return 0;
}

// 验证消息头
static int validate_msg_header(const spi_msg_header_t *header) {
    // 检查前导码 ( 前导码为 0xAA 0xAA)
    if (header->preamble[0] != 0xAA || header->preamble[1] != 0xAA) {
        printf("Invalid preamble: %02X %02X\n", header->preamble[0], header->preamble[1]);
        return -1;
    }

    SPI_MSG_DEBUG("Received header: type=%02X, id=0x%04X, length=%u", 
                header->cmd_type, header->cmd_id, header->data_length);

    return 0;
}

// 计算文件的 MD5 值并转换为十六进制字符串
static int calculate_file_md5(const char* file_path, char* md5_str) {
    char cmd[256];
    char result[256];
    FILE* fp;

    // 构建 md5sum 命令
    snprintf(cmd, sizeof(cmd), "md5sum %s", file_path);

    // 执行命令并获取输出
    fp = popen(cmd, "r");
    if (fp == NULL) {
        printf("Failed to execute md5sum command: %s\n", strerror(errno));
        return -1;
    }

    // 读取命令输出
    if (fgets(result, sizeof(result), fp) == NULL) {
        printf("Failed to read md5sum output\n");
        pclose(fp);
        return -1;
    }

    // 关闭管道
    pclose(fp);

    // 提取 MD5 值（md5sum 输出格式为：MD5值 文件名）
    char* space = strchr(result, ' ');
    if (space == NULL) {
        printf("Invalid md5sum output format\n");
        return -1;
    }
    *space = '\0';  // 截断字符串，只保留 MD5 值部分

    // 复制 MD5 值到输出缓冲区
    strncpy(md5_str, result, MD5_SIZE);
    md5_str[MD5_SIZE] = '\0';  // 确保字符串结束

    return 0;
}

// 比较两个 MD5 字符串
static int compare_md5(const char* md51, const char* md52) {
    char md51_lower[MD5_SIZE + 1];
    char md52_lower[MD5_SIZE + 1];
    
    // 转换为小写
    for (int i = 0; i < MD5_SIZE; i++) {
        md51_lower[i] = tolower(md51[i]);
        md52_lower[i] = tolower(md52[i]);
    }
    md51_lower[MD5_SIZE] = '\0';
    md52_lower[MD5_SIZE] = '\0';
    
    return strncmp(md51_lower, md52_lower, MD5_SIZE);
}

// 打印 MD5 值（用于调试）
static void print_md5(const char* prefix, const char* md5) {
    printf("%s: %s (length: %zu)\n", prefix, md5, strlen(md5));
}

static char * get_ota_status_info(void)
{
    cJSON *otaStatusJSObject = NULL;
    cJSON *devOTAInfo = NULL;
    cJSON * detail = cJSON_CreateObject();

    if(detail != NULL) {
        uint32_t cur_version = 0x00010001;      /* fake version */

        // 提取major和minor版本号
        uint16_t major_version = (cur_version >> 16) & 0xFFFF;
        uint16_t minor_version = cur_version & 0xFFFF;

        // 格式化版本号字符串
        char version_str[32];
        snprintf(version_str, sizeof(version_str), "%u.%u.0", major_version, minor_version);

        cJSON_AddItemToObject(detail, "subsystem", cJSON_CreateString("deltad-linux"));
        cJSON_AddItemToObject(detail, "status", cJSON_CreateString("success"));
        cJSON_AddItemToObject(detail, "process", cJSON_CreateNumber(g_recv_process));
        cJSON_AddItemToObject(detail, "version", cJSON_CreateString(version_str));
    }
    else {
        printf("Create detail failed\n");
        return NULL;
    }

    if((devOTAInfo = cJSON_CreateArray()) != NULL) {
        cJSON_AddItemToArray(devOTAInfo, detail);
    }
    else {
        printf("Create devOTAInfo failed\n");
        cJSON_Delete(detail);
        return NULL;
    }

    if((otaStatusJSObject = cJSON_CreateObject()) != NULL) {
        cJSON_AddItemToObject(otaStatusJSObject, "ota_subsystem_status", devOTAInfo);

        // 将JSON对象转换为字符串
        char *json_string = cJSON_Print(otaStatusJSObject);
        // 释放JSON对象
        cJSON_Delete(otaStatusJSObject);

        return json_string;
    }
    else {
        printf("Create otaStatusJSObject failed\n");
        cJSON_Delete(devOTAInfo);
        return NULL;
    }
}

void process_json_ota_ctrl_cmd(cJSON *json_obj, int spi_fd, uint16_t cmd_id)
{
    // 获取命令类型
    cJSON *cmd_obj = cJSON_GetObjectItem(json_obj, "cmd");
    if (!cmd_obj || !cJSON_IsString(cmd_obj)) {
        printf("Missing or invalid 'cmd' field in JSON\n");
        send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
        return;
    }
    const char* cmd = cmd_obj->valuestring;
    if (strcmp(cmd, "start") == 0) {
        // 计算接收到的JSON数据的CRC16
#if 0
        uint16_t received_crc = calculate_crc16((const uint8_t*)data, header->data_length);
        if (received_crc != header->crc) {
            printf("CRC verification failed for JSON data. Received CRC: 0x%04X, Expected CRC: 0x%04X\n", 
                received_crc, header->crc);
            send_ack_message(spi_fd, header->cmd_id, STATUS_ERROR);
            return -1;
        }
#endif
        // 获取设备类型
        cJSON *device_obj = cJSON_GetObjectItem(json_obj, "device");
        if (!device_obj || !cJSON_IsString(device_obj)) {
            printf("Missing or invalid 'device' field in JSON\n");
            send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
            return;
        }
        const char* device = device_obj->valuestring;
        printf("Device type: %s\n", device);

        // 获取 MD5 值
        cJSON *md5_obj = cJSON_GetObjectItem(json_obj, "md5");
        if (!md5_obj || !cJSON_IsString(md5_obj)) {
            printf("Missing or invalid 'md5' field in JSON\n");
            send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
            return;
        }
        const char* md5 = md5_obj->valuestring;
        if (strlen(md5) != MD5_SIZE) {
            printf("Invalid MD5 length: %zu\n", strlen(md5));
            send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
            return;
        }

        // 保存 MD5 值供后续使用
        strncpy(g_expected_md5, md5, MD5_SIZE);
        g_expected_md5[MD5_SIZE] = '\0';

        printf("MD5 value: %s\n", g_expected_md5);

        // 获取文件长度
        cJSON *file_length = cJSON_GetObjectItem(json_obj, "file_len");
        if (!file_length || !cJSON_IsNumber(file_length)) {
            printf("Missing or invalid 'file_len' field in JSON\n");
            send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
            return;
        }
        int transfer_file_len = file_length->valueint;
        if (!transfer_file_len) {
            printf("file length is 0\n");
            send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
            return;
        }
        g_file_tol_len = transfer_file_len;
        g_spi_ota_phase = SPI_OTA_PHASE_CMD_START;
        printf("file length: %d\n", g_file_tol_len);

        g_spi_recv_len = SPI_FILE_TRANSFER_FRAME_SIZE;
        // 发送开始确认消息
        if (send_ack_message(spi_fd, cmd_id, STATUS_SUCCESS) != 0) {
            return;
        }
    }
    else if (strcmp(cmd, "end") == 0) {
        // 验证文件 MD5
        char calculated_md5[MD5_SIZE + 1] = { 0 };
        
        g_spi_ota_phase = SPI_OTA_PHASE_CMD_END;

        if (calculate_file_md5(spi_ota_file, calculated_md5) != 0) {
            printf("Failed to calculate MD5\n");
            send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
            return;
        }

        // 比较 MD5 值
        if (compare_md5(calculated_md5, g_expected_md5) != 0) {
            printf("MD5 verification failed\n");
            print_md5("Expected MD5", g_expected_md5);
            print_md5("Calculated MD5", calculated_md5);
            send_ack_message(spi_fd, cmd_id, STATUS_MD5_ERROR);
            return;
        }

        // 发送结束确认消息
        if (send_ack_message(spi_fd, cmd_id, STATUS_SUCCESS) != 0) {
            printf("end ack send failure: %s\n");
            return;
        }

        // 触发升级
        struct bootloader_message boot = {};
        strcpy(boot.recovery, "recovery");
        strcat(boot.recovery, " --update_package=/rom/mnt");
        printf("boot parameter: %s\n", boot.recovery);
        strcpy(boot.cmd, "boot-recovery");
        set_bootloader_message(&boot, gDataBlock);
        reboot(RB_AUTOBOOT);
    } else if (strcmp(cmd, "poll") == 0) {
        //Should update receive buf length
        if(g_spi_ota_phase == SPI_OTA_PHASE_CMD_START) {
            printf("update recv len: %d\n", g_spi_recv_len);
            g_spi_recv_len = SPI_FILE_TRANSFER_FRAME_SIZE;
        }
    }
    else {
        printf("Invalid command: %s\n", cmd);
        send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
        return;
    }
}

void process_json_ota_set_cmd(cJSON *root, int spi_fd)
{
    // 获取命令
    cJSON *cmd_obj = cJSON_GetObjectItem(root, "cmd");
    if (cmd_obj == NULL || !cJSON_IsString(cmd_obj)) {
        printf("No valid 'cmd' field in JSON\n");
        send_ack_message(spi_fd, CMD_ID_OTA_SET, STATUS_FAILURE);
        return;
    }
    
    char *cmd = cmd_obj->valuestring;
    
    if (strcmp(cmd, "reboot") == 0 || strcmp(cmd, "shutdown") == 0) {
        // 处理重启命令
        cmd_obj = cJSON_GetObjectItem(root, "switch_to_new");
        if (cmd_obj == NULL || !cJSON_IsBool(cmd_obj)) {
            printf("No valid 'switch_to_new' field in JSON\n");
            send_ack_message(spi_fd, CMD_ID_OTA_SET, STATUS_FAILURE);
            return;
        }
        if(cJSON_IsFalse(cmd_obj)) {
            // 切换到旧版本
            //
        }

        //send_ack_message(spi, CMD_ID_OTA_SET, CMD_TYPE_POST, STATUS_SUCCESS);
        // 触发升级
        struct bootloader_message boot = {};
        strcpy(boot.recovery, "recovery");
        strcat(boot.recovery, " --update_package=/rom/mnt");
        printf("boot parameter: %s\n", boot.recovery);
        strcpy(boot.cmd, "boot-recovery");
        set_bootloader_message(&boot, gDataBlock);
        reboot(RB_AUTOBOOT);
    }
}

static void process_json_command(int spi_fd, uint16_t cmd_id, const char *data, uint16_t length)
{
    // 解析 JSON
    cJSON *json_obj = cJSON_Parse(data);
    if (!json_obj) {
        printf("Failed to parse JSON data: %s\n", cJSON_GetErrorPtr());
        send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
        return;
    }

    // 打印完整的 JSON 内容
    char *json_str = cJSON_Print(json_obj);
    if (json_str) {
        printf("Received JSON content:\n%s\n", json_str);
        free(json_str);
    }

    switch(cmd_id) {
        case CMD_ID_OTA_CONTROL_START:
        //case CMD_ID_OTA_CONTROL_END:
            process_json_ota_ctrl_cmd(json_obj, spi_fd, cmd_id);
            break;
        case CMD_ID_OTA_SET:
            process_json_ota_set_cmd(json_obj, spi_fd);
            break;
    }

    // 清理 JSON 对象
    cJSON_Delete(json_obj);
}

void process_ota_ctrl_end_cmd(int spi_fd, uint16_t cmd_id)
{
    // 验证文件 MD5
    char calculated_md5[MD5_SIZE + 1] = { 0 };
    
    g_spi_ota_phase = SPI_OTA_PHASE_CMD_END;

    if (calculate_file_md5(spi_ota_file, calculated_md5) != 0) {
        printf("Failed to calculate MD5\n");
        send_ack_message(spi_fd, cmd_id, STATUS_FAILURE);
        return;
    }

    // 比较 MD5 值
    if (compare_md5(calculated_md5, g_expected_md5) != 0) {
        printf("MD5 verification failed\n");
        print_md5("Expected MD5", g_expected_md5);
        print_md5("Calculated MD5", calculated_md5);
        send_ack_message(spi_fd, cmd_id, STATUS_MD5_ERROR);
        return;
    }

    // 发送结束确认消息
    if (send_ack_message(spi_fd, cmd_id, STATUS_SUCCESS) != 0) {
        printf("end ack send failure: %s\n");
        return;
    }

#if 1
    char * json_string = get_ota_status_info();
    printf("json_string: %s\n", json_string);
    printf("json_string len: %d\n", strlen(json_string));
    if (json_string != NULL) {
        // 发送响应
        send_response(spi_fd, CMD_ID_OTA_STATUS, CMD_TYPE_POST, STATUS_SUCCESS, (uint8_t *)json_string, strlen(json_string));

        // 释放JSON字符串内存
        free(json_string);
    }
#endif
}

static void process_ota_data_command(int spi_fd, uint8_t *data, uint16_t data_len)
{
    static int file_fd = -1;
    static uint32_t total_received = 0;
    static uint8_t process_printed = 0;

    // 如果是第一次接收，创建文件
    if (file_fd < 0) {
        file_fd = open(spi_ota_file, O_WRONLY | O_CREAT | O_TRUNC, 0644);
        if (file_fd < 0) {
            printf("Failed to create file: %s\n", strerror(errno));
            send_ack_message(spi_fd, CMD_ID_OTA_DATA, STATUS_FAILURE);
            return;
        }
        total_received = 0;
        g_recv_process = 0;
    }

    if(total_received + data_len > g_file_tol_len) {
        data_len = g_file_tol_len - total_received;
    }

    // 写入文件
    if (write(file_fd, data, data_len) != data_len) {
        printf("Failed to write data: %s\n", strerror(errno));
        send_ack_message(spi_fd, CMD_ID_OTA_DATA, STATUS_FAILURE);
        close(file_fd);
        file_fd = -1;
        return;
    }

    total_received += data_len;

    // 显示进度
    g_recv_process = (total_received * 100) / g_file_tol_len;
    if(g_recv_process % 5 == 0) {
        if(!process_printed) {
            printf("Progress: %u%%\n", g_recv_process);
            process_printed = 1;
        }
    }
    else if(process_printed) process_printed = 0;

    // 检查是否接收完成
    if (total_received >= g_file_tol_len) {
        close(file_fd);
        printf("Transfer finished\n");
        file_fd = -1;
        g_spi_recv_len = SPI_MSG_DEFAULTE_FRAME_SIZE;
    }
}

static void process_ota_status_cmd(int spi_fd)
{
    // 处理开始命令
    char * json_string = get_ota_status_info();
    if (json_string != NULL) {
        // 发送响应
        send_response(spi_fd, CMD_ID_OTA_STATUS, CMD_TYPE_GET_RESP, STATUS_SUCCESS, (uint8_t *)json_string, strlen(json_string));

        // 释放JSON字符串内存
        free(json_string);
    } else {
        printf("Failed to get valid ota status info\n");
        send_ack_message(spi_fd, CMD_ID_OTA_STATUS, STATUS_FAILURE);
    }
}
// 处理 SPI 消息
static int handle_spi_message(int spi_fd, const spi_msg_header_t* header, const char* data, size_t data_len) {
    if (!header) {
        return -1;
    }

    // 根据命令类型处理消息
    switch (header->cmd_id) {
        case CMD_ID_OTA_CONTROL_START:
        case CMD_ID_OTA_SET: {
            if(!data) {
                printf("data should not be NULL for msg: 0x%4x\n", header->cmd_id);
                send_ack_message(spi_fd, header->cmd_id, STATUS_FAILURE);
                return -1;
            }
            process_json_command(spi_fd, header->cmd_id, data, data_len);
            break;
        }
        case CMD_ID_OTA_STATUS:
            process_ota_status_cmd(spi_fd);
            break;
        case CMD_ID_OTA_DATA: {
            if(!data) {
                printf("data should not be NULL for msg: 0x%4x\n", header->cmd_id);
                send_ack_message(spi_fd, header->cmd_id, STATUS_FAILURE);
                return -1;
            }
            process_ota_data_command(spi_fd, data, data_len);
            break;
        }
        case CMD_ID_OTA_CONTROL_END:
            process_ota_ctrl_end_cmd(spi_fd, header->cmd_id);
            break;

        default:
            printf("Unknown command id: %02X\n", header->cmd_id);
            send_ack_message(spi_fd, header->cmd_id, STATUS_FAILURE);
            return -1;
    }

    return 0;
}

// SPI 接收线程函数
static void* spi_receive_thread(void* arg) {
    const char* spi_ota_path = "/rom/mnt/ota";
    int spi_fd = -1;
    
    printf("spi_receive_thread started\n");

    // 检查并删除 spi_ota_path 目录
    struct stat st;
    if (stat(spi_ota_path, &st) == 0) {
        // 删除目录及其内容
        char cmd[256];
        snprintf(cmd, sizeof(cmd), "rm -rf %s", spi_ota_path);
        if (system(cmd) != 0) {
            printf("Failed to remove directory %s\n", spi_ota_path);
        } else {
            printf("Successfully removed directory %s\n", spi_ota_path);
        }
    }

    // 检查目录是否存在，不存在则创建
    if (stat(spi_ota_path, &st) != 0) {
        if (mkdir(spi_ota_path, 0755) != 0) {
            printf("Failed to create directory %s: %s\n", spi_ota_path, strerror(errno));
            return NULL;
        }
    }

    char *msg_buf = (char *)malloc(SPI_MSG_MAX_SIZE); // 使用固定2K长度的buffer

    if(!msg_buf) {
        printf("Failed to malloc msg_buf and directly return the thread\n");
        return NULL;
    }

    while (1) {
        pthread_mutex_lock(&g_spi_thread.mutex);
        if (!g_spi_thread.running) {
            pthread_mutex_unlock(&g_spi_thread.mutex);
            break;
        }
        pthread_mutex_unlock(&g_spi_thread.mutex);

        if (spi_fd < 0) {
            spi_fd = init_spi();
            if (spi_fd < 0) {
                sleep(3);
                continue;
            }
            printf("spi init ok and start to read\n");
        }

        ssize_t received = spi_read(spi_fd, msg_buf, g_spi_recv_len);
        if (received < 1) {
            printf("SPI read failed\n");
            continue;
        }

        SPI_MSG_DEBUG("spi receive %d this time\n", received);

        if (received < sizeof(spi_msg_header_t)) {
            printf("Received too few bytes for header: %zd\n", received);
            continue;
        }

        // 解析消息头
        spi_msg_header_t header;
        memcpy(&header, msg_buf, sizeof(spi_msg_header_t));
#if DEBUG_SPI_MSG
        for(int i = 0; i < received; i++) {
            printf("%02X ", msg_buf[i]);
        }
        printf("\n");
#endif
        // 校验头部
        if (validate_msg_header(&header) != 0) {
            continue;
        }

        const char *data = (header.data_length > 0) ? 
            (msg_buf + sizeof(spi_msg_header_t)) : NULL;
        
        if (handle_spi_message(spi_fd, &header, data, header.data_length) != 0) {
            printf("Failed to handle message\n");
        }
    }

    if (spi_fd >= 0) {
        close(spi_fd);
    }

    if(msg_buf) {
        free(msg_buf);
    }

    return NULL;
}

// 启动 SPI 接收线程
int start_spi_receive_thread(void) {
    pthread_mutex_lock(&g_spi_thread.mutex);
    
    if (g_spi_thread.running) {
        pthread_mutex_unlock(&g_spi_thread.mutex);
        return 0;
    }

    g_spi_thread.running = 1;
    pthread_mutex_unlock(&g_spi_thread.mutex);

    // 创建线程
    int ret = pthread_create(&g_spi_thread.thread_id, NULL, 
                           spi_receive_thread, NULL);
    if (ret != 0) {
        printf("Failed to create SPI receive thread: %s\n", strerror(ret));
        pthread_mutex_lock(&g_spi_thread.mutex);
        g_spi_thread.running = 0;
        pthread_mutex_unlock(&g_spi_thread.mutex);
        return -1;
    }

    // 设置线程为分离状态
    pthread_detach(g_spi_thread.thread_id);
    
    return 0;
}

// 停止 SPI 接收线程
void stop_spi_receive_thread(void) {
    pthread_mutex_lock(&g_spi_thread.mutex);
    if (g_spi_thread.running) {
        g_spi_thread.running = 0;
        pthread_cond_signal(&g_spi_thread.cond);
    }
    pthread_mutex_unlock(&g_spi_thread.mutex);
}

